import {
  showToast as vantShowToast,
  showLoadingToast as vantShowLoadingToast,
  closeToast as vantCloseToast
} from "vant";

// 普通提示
export function showToast(message: string, duration: number = 2000) {
  vantShowToast({
    message,
    color: "#fff",
    background: "#333",
    position: "middle",
    duration
  });
}

// 成功提示
export function showSuccessToast(message: string, duration: number = 2000) {
  vantShowToast({
    message,
    type: "success",
    color: "#fff",
    background: "#333",
    position: "middle",
    duration
  });
}

// 失败提示
export function showFailToast(message: string, duration: number = 2000) {
  vantShowToast({
    message,
    type: "fail",
    color: "#fff",
    background: "#333",
    position: "middle",
    duration
  });
}

// 警告提示
export function showWarningToast(message: string, duration: number = 2000) {
  vantShowToast({
    message,
    color: "#fff",
    background: "#333",
    icon: "warning-o",
    position: "middle",
    duration
  });
}

// 加载提示
export function showLoadingToast(message: string = "加载中...") {
  vantShowLoadingToast({
    message,
    duration: 0, // 持续展示 toast
    forbidClick: true, // 禁用背景点击
    loadingType: "spinner",
    color: "#fff",
    background: "#333"
  });
}

// 清除所有Toast
export function clearToast() {
  vantCloseToast();
}
