import axios from "axios";
import { showFailToast } from "../utils/toast";

// 创建axios实例
const api = axios.create({
  baseURL: "/cph",
  timeout: 10000
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里统一添加token
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    console.error("请求拦截器错误:", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 直接返回响应数据
    return response;
  },
  (error) => {
    // 统一处理错误
    if (error.response) {
      // 服务器返回错误状态码
      switch (error.response.status) {
        case 401:
          showFailToast("未授权，请重新登录");
          // 可以在这里处理登出逻辑
          break;
        case 403:
          showFailToast("拒绝访问");
          break;
        case 404:
          showFailToast("请求的资源不存在");
          break;
        case 500:
          showFailToast("服务器错误");
          break;
        default:
          showFailToast(`${error.response.message}`);
      }
    } else if (error.request) {
      // 请求发出但没有收到响应
      showFailToast("网络错误，请检查您的网络连接");
    } else {
      // 请求配置出错
      showFailToast("请求配置错误");
    }
    console.error("响应拦截器错误:", error);
    return Promise.reject(error);
  }
);

// 获取部门列表
export async function getDepartments() {
  try {
    const response = await api.get("/app/deptList");
    return response.data;
  } catch (error) {
    console.error("获取部门列表失败:", error);
    throw error;
  }
}

// 根据部门ID获取科室列表
export async function getOfficesByDepartment(departmentId: string) {
  try {
    const response = await api.get(`/app/sectionList?deptCode=${departmentId}`);
    return response.data;
  } catch (error) {
    console.error("获取科室列表失败:", error);
    throw error;
  }
}

// 根据科室ID获取工号列表
export async function getWorkIdsByOffice(officeId: string) {
  try {
    const response = await api.get(`/workIds?officeId=${officeId}`);
    return response.data;
  } catch (error) {
    console.error("获取工号列表失败:", error);
    throw error;
  }
}

// 获取员工工号
export async function getEmployeeWorkId(userInfo: any) {
  try {
    const response = await api.get(
      `/app/userInfoList?deptCode=${userInfo.deptCode}&sectionCode=${userInfo.sectionCode}`
    );
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error("获取员工工号失败:", error);
    throw error;
  }
}
// 验证工号是否有效
export async function validateWorkId(secCode: string, userCode: string) {
  try {
    const response = await api.get(
      `/app/userInfo?secCode=${secCode}&userCode=${userCode}`
    );
    return response.data;
  } catch (error) {
    console.error("验证工号失败:", error);
    throw error;
  }
}

// 注册用户
export async function registerUser(userInfo: any) {
  try {
    const response = await api.post("/app/userRegister", userInfo);
    return response.data;
  } catch (error) {
    console.error("注册用户失败:", error);
    throw error;
  }
}
// 提交问卷
export async function submitSurvey(surveyInfo: any) {
  try {
    const response = await api.post("/app/checkIn", surveyInfo);
    return response.data;
  } catch (error) {
    console.error("提交问卷失败:", error);
    throw error;
  }
}

// 编辑问卷
export async function updateSurvey(surveyInfo: any) {
  try {
    const response = await api.post("/app/checkInUpdate", surveyInfo);
    return response.data;
  } catch (error) {
    console.error("更新问卷失败:", error);
    throw error;
  }
}

// 打卡详情
export async function getCheckInDetail(userInfo: any) {
  try {
    const response = await api.get(
      `/app/userCheckDetail?userCode=${userInfo.userCode}&checkDate=${userInfo.checkDate}`
    );
    return response.data;
  } catch (error) {
    console.error("获取打卡详情失败:", error);
    throw error;
  }
}

// 打分记录
export async function getScoreRecord(userInfo: any) {
  try {
    const response = await api.post("/app/checkInAppList", userInfo);
    return response.data;
  } catch (error) {
    console.error("获取打分记录失败:", error);
    throw error;
  }
}
export default {
  getDepartments,
  getOfficesByDepartment,
  getWorkIdsByOffice,
  validateWorkId
};
