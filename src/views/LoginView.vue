<template>
  <div class="login-container">
    <div class="logo-area">
      <h2>欢迎参加</h2>
      <h2>2025年环境月"碳普惠"活动</h2>
    </div>
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <!-- 部门选择 -->
        <van-field
          v-model="departmentName"
          is-link
          readonly
          name="department"
          label="部门"
          placeholder="请选择部门"
          @click="showDepartmentPicker = true"
          :rules="[{ required: true, message: '请选择部门' }]"
        />
        <van-popup v-model:show="showDepartmentPicker" position="bottom">
          <van-picker
            :columns="departmentList"
            @confirm="onDepartmentConfirm"
            @cancel="showDepartmentPicker = false"
            show-toolbar
            title="选择部门"
          />
        </van-popup>

        <!-- 科室选择 -->
        <van-field
          v-model="officeName"
          is-link
          readonly
          name="office"
          label="科室"
          placeholder="请选择科室"
          @click="showOfficePicker = true"
          :disabled="!selectedDepartment"
          :rules="[{ required: true, message: '请选择科室' }]"
        />
        <van-popup v-model:show="showOfficePicker" position="bottom">
          <van-picker
            :columns="officeList"
            @confirm="onOfficeConfirm"
            @cancel="showOfficePicker = false"
            title="选择科室"
          />
        </van-popup>

        <!-- 工号输入 -->
        <van-field
          v-model="workId"
          is-link
          readonly
          name="workId"
          label="工号"
          placeholder="请选择工号"
          @click="showWorkIdPicker = true"
          :disabled="!officeName"
          :rules="[{ required: true, message: '请选择工号' }]"
        />
        <van-popup v-model:show="showWorkIdPicker" position="bottom">
          <van-picker
            :columns="workIdList"
            @confirm="onWorkIdConfirm"
            @cancel="showWorkIdPicker = false"
            title="选择工号"
          />
        </van-popup>

        <!-- 日期选择 -->
        <van-field
          v-model="date"
          is-link
          readonly
          name="date"
          label="日期"
          placeholder="请选择日期"
          @click="showDatePicker = true"
          :rules="[{ required: true, message: '请选择日期' }]"
        />
        <van-popup v-model:show="showDatePicker" position="bottom">
          <van-date-picker
            v-model="currentDate"
            title="选择日期"
            :min-date="minDate"
            :max-date="maxDate"
            @confirm="onDateConfirm"
            @cancel="showDatePicker = false"
          />
        </van-popup>
      </van-cell-group>

      <div style="margin: 16px">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :disabled="!workIdValid && !workId"
        >
          进入打卡
        </van-button>
        <div class="register-link">
          还没有账号？<router-link to="/register">立即注册</router-link>
        </div>
      </div>
    </van-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import {
  showFailToast,
  showLoadingToast,
  clearToast,
  showSuccessToast
} from "../utils/toast";
import { useUserStore } from "../pinia/modules/user";
import {
  getDepartments,
  getOfficesByDepartment,
  validateWorkId,
  getCheckInDetail,
  getEmployeeWorkId
} from "@/api/organization";
import { useRouter } from "vue-router";
import dayjs from "dayjs";

const router = useRouter();
const userStore = useUserStore();
// 添加用户信息变量
const userInfo = ref<any>(null);

// 部门相关
const departmentName = ref("");
const selectedDepartment = ref(null);
const departmentList = ref([]);
const showDepartmentPicker = ref(false);

// 科室相关
const officeName = ref("");
const selectedOffice = ref(null);
const officeList = ref([]);
const showOfficePicker = ref(false);

// 工号相关
const workId = ref("");
const workIdValid = ref(false);
const workIdMessage = ref("");
const showWorkIdPicker = ref(false);
const workIdList = ref([]);

// 日期相关
const date = ref("");
const currentDate = ref(["2025", "05", "26"]);
const minDate = new Date(2025, 0, 1);
const maxDate = new Date(2025, 12, 31);
const showDatePicker = ref(false);

// 验证工号的原始函数
const validateWorkInfo = async () => {
  if (!workId.value || !selectedOffice.value) return;

  try {
    showLoadingToast("验证工号...");
    const res = await validateWorkId(selectedOffice.value, workId.value);
    clearToast();

    if (res.code === 0 && res.data != null) {
      // 工号有效
      workIdValid.value = true;
      workIdMessage.value = "";
      // 保存用户信息以便登录时使用
      userInfo.value = res.data;
    } else {
      // 工号无效
      workIdValid.value = false;
      workIdMessage.value = res.message || "工号输入错误或不存在";
      showFailToast(workIdMessage.value);
    }
  } catch (error) {
    clearToast();
    workIdValid.value = false;
    workIdMessage.value = error.message || "验证工号时出错";
    showFailToast(workIdMessage.value);
  }
};

// 获取部门列表
const fetchDepartmentList = async () => {
  try {
    let res = await getDepartments();
    console.log("res", res);
    if (res.code === 0) {
      departmentList.value = res.data.map((item) => ({
        text: item.deptName,
        value: item.deptCode
      }));
    }
  } catch (error) {
    clearToast();
    showFailToast("获取部门列表失败");
  }
};

// 获取科室列表
const fetchOfficeList = async (departmentId: any) => {
  try {
    showLoadingToast("加载科室列表...");
    // 模拟API请求
    let res = await getOfficesByDepartment(departmentId);
    console.log("res", res);
    officeList.value = res.data.map((item: any) => ({
      text: item.secName,
      value: item.secCode
    }));
    clearToast();
  } catch (error) {
    showFailToast("获取科室列表失败");
    clearToast();
  }
};

// 获取工号列表
const fetchWorkIdList = async (userInfo: any) => {
  try {
    showLoadingToast("加载工号列表...");
    let res = await getEmployeeWorkId(userInfo);
    console.log("res", res);
    if (res.code === 0) {
      workIdList.value = res.data.map((item: any) => ({
        text: `${item.userName}（${item.userCode}）`,
        value: item.userCode
      }));
    }
    clearToast();
  } catch (error) {
    showFailToast("获取工号列表失败");
    clearToast();
  }
};

// 部门选择确认
const onDepartmentConfirm = ({ selectedValues, selectedOptions }) => {
  selectedDepartment.value = selectedValues;
  departmentName.value = selectedOptions[0]?.text;
  showDepartmentPicker.value = false;

  // 重置科室和工号
  officeName.value = "";
  selectedOffice.value = null;

  // 获取对应的科室列表
  fetchOfficeList(selectedValues);
};

// 科室选择确认
const onOfficeConfirm = ({ selectedValues, selectedOptions }) => {
  selectedOffice.value = selectedValues;
  officeName.value = selectedOptions[0]?.text;
  showOfficePicker.value = false;

  // 重置工号
  workId.value = "";

  // 获取对应的工号列表
  fetchWorkIdList({
    deptCode: selectedDepartment.value,
    sectionCode: selectedOffice.value
  });
};

// 工号选择确认
const onWorkIdConfirm = ({ selectedValues }: any) => {
  workId.value = selectedValues;
  showWorkIdPicker.value = false;
  validateWorkInfo();
};

// 日期选择确认
const onDateConfirm = ({ selectedValues }: any) => {
  const [year, month, day] = selectedValues;
  date.value = `${year}-${month}-${day}`;
  currentDate.value = selectedValues;
  console.log("currentDate", currentDate.value, date.value);
  showDatePicker.value = false;
};

// 获取是否打卡  已经打卡过则提醒
const checkInDetail = async () => {
  try {
    const res = await getCheckInDetail({
      userCode: workId.value,
      checkDate: date.value
    });
    console.log(res);
    if (res.code === 0 && res.data) {
      showSuccessToast("您今天已经打过卡啦！明天再来吧！");
      return;
    } else {
      // 调用 store 中的登录方法
      try {
        const loginData = {
          deptCode: selectedDepartment.value,
          deptName: departmentName.value,
          secCode: selectedOffice.value,
          secName: officeName.value,
          userCode: workId.value,
          userName: userInfo.value.userName
        };

        // 使用验证工号时获取的用户信息
        userStore.loginWithDetails(loginData, userInfo.value);

        // 直接跳转到打卡页面，让路由守卫添加查询参数
        router.push("/survey");
      } catch (error) {
        console.error("登录错误:", error);
      }
    }
  } catch (error) {
    console.error("获取打卡详情失败:", error);
    return;
  }
};
const onSubmit = () => {
  if (!selectedDepartment.value || !selectedOffice.value || !workId.value) {
    showFailToast("请完成所有选择");
    return;
  }
  if (!workIdValid.value) {
    showFailToast(workIdMessage.value || "工号无效，请重新输入");
    return;
  }
  checkInDetail();
};

// 初始化获取部门列表
onMounted(async () => {
  await fetchDepartmentList();
});
</script>

<style scoped>
.login-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: url("/assets/bg.jpg") no-repeat center center;
  background-size: cover;
}

.logo-area {
  text-align: center;
  margin-bottom: 40px;
}

.logo-area img {
  width: 80px;
  height: 80px;
}

.register-link {
  margin-top: 16px;
  text-align: center;
  font-size: 14px;
}
</style>
