<template>
  <div class="survey-container">
    <van-nav-bar title="碳普惠活动问卷" />
    <div class="survey-header">
      <h3 class="survey-title">碳普惠活动</h3>
      <p>完成上传碳普惠"树林"——碳普惠实践活动方式</p>
      <!-- <p class="download-link">查看实施说明.pdf</p> -->
    </div>

    <!-- 准备状态选择 -->
    <div v-if="!showFullSurvey" class="preparation-section">
      <van-cell-group inset title="1. 亲爱的员工，需要相关数据准备，你都准备好了吗?">
        <van-field name="preparationStatus" label="">
          <template #input>
            <van-radio-group v-model="preparationStatus" @change="handlePreparationChange">
              <van-radio name="ready" label-position="right">
                <div class="radio-content">
                  <div>准备好了</div>
                  <div class="radio-description">
                    您好，在填写本次调研前，请确保您已经准备好相关数据，包括：过去3个月能源、水工作量数据、过去3个月能源、水费用数据等。
                  </div>
                </div>
              </van-radio>
              <van-radio name="notReady" label-position="right">
                <div class="radio-content">
                  <div>还未准备好</div>
                </div>
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>
      </van-cell-group>

      <div v-if="preparationStatus === 'notReady'" style="margin: 16px">
        <van-button round block type="primary" @click="goBack">
          返回准备
        </van-button>
      </div>

      <div v-if="preparationStatus === 'ready'" style="margin: 16px">
        <van-button round block type="primary" @click="proceedToSurvey">
          继续
        </van-button>
      </div>
    </div>

    <!-- 完整问卷内容 -->
    <van-form v-if="showFullSurvey" @submit="onSubmit">
      <van-cell-group inset title="基本信息">
        <van-field v-model="form.name" name="name" label="姓名" readonly />
        <van-field v-model="form.department" name="department" label="部门" readonly />
      </van-cell-group>

      <!-- 第一项：微信步数 -->
      <van-cell-group inset>
        <template #title>
          <div class="section-title">1、微信步数(这五天走了多少步呀)</div>
          <div class="clear-selection" @click="clearCount('stepCount')">
            清除选择
          </div>
        </template>
        <van-field name="stepCount">
          <template #input>
            <van-radio-group v-model="form.stepCount" direction="vertical" @change="validatePhotos('stepCount')">
              <van-cell :title="StepCountRangeText[StepCountRange.RANGE_20K_25K]" clickable
                @click="form.stepCount = StepCountRange.RANGE_20K_25K">
                <template #right-icon><van-radio :name="StepCountRange.RANGE_20K_25K" /></template>
              </van-cell>
              <van-cell :title="StepCountRangeText[StepCountRange.RANGE_25K_30K]" clickable
                @click="form.stepCount = StepCountRange.RANGE_25K_30K">
                <template #right-icon><van-radio :name="StepCountRange.RANGE_25K_30K" /></template>
              </van-cell>
              <van-cell :title="StepCountRangeText[StepCountRange.RANGE_30K_35K]" clickable
                @click="form.stepCount = StepCountRange.RANGE_30K_35K">
                <template #right-icon><van-radio :name="StepCountRange.RANGE_30K_35K" /></template>
              </van-cell>
              <van-cell :title="StepCountRangeText[StepCountRange.RANGE_ABOVE_35K]" clickable
                @click="form.stepCount = StepCountRange.RANGE_ABOVE_35K">
                <template #right-icon><van-radio :name="StepCountRange.RANGE_ABOVE_35K" /></template>
              </van-cell>
            </van-radio-group>
          </template>
        </van-field>
        <van-field name="stepCountPhotos" :rules="[
          { required: form.stepCount !== '', message: '请上传步数照片' }
        ]">
          <template #input>
            <van-uploader v-model="form.stepCountFileList" accept="image/*" :before-read="beforeRead"
              :after-read="afterRead" :max-size="5 * 1024 * 1024" :max-count="5" @oversize="onOversize"
              :preview-image="true" />
          </template>
        </van-field>
      </van-cell-group>

      <!-- 第二项：共享单车 -->
      <van-cell-group inset>
        <template #title>
          <div class="section-title" style="width: 5rem !important">
            2、共享单车次数(这五天骑了几次共享单车了呀)
          </div>
          <div class="clear-selection" @click="clearCount('bikeUsage')">
            清除选择
          </div>
        </template>
        <van-field name="bikeUsage">
          <template #input>
            <van-radio-group v-model="form.bikeUsage" direction="vertical" @change="validatePhotos('bikeUsage')">
              <van-cell :title="BikeUsageRangeText[BikeUsageRange.RANGE_3_5]" clickable
                @click="form.bikeUsage = BikeUsageRange.RANGE_3_5">
                <template #right-icon><van-radio :name="BikeUsageRange.RANGE_3_5" /></template>
              </van-cell>
              <van-cell :title="BikeUsageRangeText[BikeUsageRange.RANGE_6_8]" clickable
                @click="form.bikeUsage = BikeUsageRange.RANGE_6_8">
                <template #right-icon><van-radio :name="BikeUsageRange.RANGE_6_8" /></template>
              </van-cell>
              <van-cell :title="BikeUsageRangeText[BikeUsageRange.RANGE_9_12]" clickable
                @click="form.bikeUsage = BikeUsageRange.RANGE_9_12">
                <template #right-icon><van-radio :name="BikeUsageRange.RANGE_9_12" /></template>
              </van-cell>
            </van-radio-group>
          </template>
        </van-field>
        <van-field name="bikeUsagePhotos" :rules="[
          { required: form.bikeUsage !== '', message: '请上传骑行照片' }
        ]">
          <template #input>
            <van-uploader v-model="form.bikeUsageFileList" :max-count="10" :before-read="beforeRead"
              :after-read="afterRead" accept="image/*" @oversize="onOversize" :preview-image="true" />
          </template>
        </van-field>
      </van-cell-group>

      <!-- 第三项：公共交通 -->
      <van-cell-group inset>
        <template #title>
          <div class="section-title">3、班车/地铁/公交次数</div>
          <div class="clear-selection" @click="clearCount('publicTransport')">
            清除选择
          </div>
        </template>
        <van-field name="publicTransport">
          <template #input>
            <van-radio-group v-model="form.publicTransport" direction="vertical"
              @change="validatePhotos('publicTransport')">
              <van-cell :title="PublicTransportRangeText[PublicTransportRange.RANGE_BELOW_4]
                " clickable @click="
                  form.publicTransport = PublicTransportRange.RANGE_BELOW_4
                  ">
                <template #right-icon><van-radio :name="PublicTransportRange.RANGE_BELOW_4" /></template>
              </van-cell>
              <van-cell :title="PublicTransportRangeText[PublicTransportRange.RANGE_4_6]
                " clickable @click="form.publicTransport = PublicTransportRange.RANGE_4_6">
                <template #right-icon><van-radio :name="PublicTransportRange.RANGE_4_6" /></template>
              </van-cell>
              <van-cell :title="PublicTransportRangeText[PublicTransportRange.RANGE_7_9]
                " clickable @click="form.publicTransport = PublicTransportRange.RANGE_7_9">
                <template #right-icon><van-radio :name="PublicTransportRange.RANGE_7_9" /></template>
              </van-cell>
            </van-radio-group>
          </template>
        </van-field>
        <van-field name="publicTransportPhotos" :rules="[
          { required: form.publicTransport !== '', message: '请上传乘车照片' }
        ]">
          <template #input>
            <van-uploader v-model="form.publicTransportFileList" :max-count="10" :before-read="beforeRead"
              :after-read="afterRead" accept="image/*" @oversize="onOversize" :preview-image="true" />
          </template>
        </van-field>
      </van-cell-group>

      <!-- 第四项：外卖不要餐具 -->
      <van-cell-group inset>
        <template #title>
          <div class="section-title">4、外卖不要餐具次数</div>
          <div class="clear-selection" @click="clearCount('noUtensils')">
            清除选择
          </div>
        </template>
        <van-field name="noUtensils">
          <template #input>
            <van-radio-group v-model="form.noUtensils" direction="vertical" @change="validatePhotos('noUtensils')">
              <van-cell :title="NoUtensilsRangeText[NoUtensilsRange.RANGE_BELOW_4]" clickable
                @click="form.noUtensils = NoUtensilsRange.RANGE_BELOW_4">
                <template #right-icon><van-radio :name="NoUtensilsRange.RANGE_BELOW_4" /></template>
              </van-cell>
              <van-cell :title="NoUtensilsRangeText[NoUtensilsRange.RANGE_4_6]" clickable
                @click="form.noUtensils = NoUtensilsRange.RANGE_4_6">
                <template #right-icon><van-radio :name="NoUtensilsRange.RANGE_4_6" /></template>
              </van-cell>
              <van-cell :title="NoUtensilsRangeText[NoUtensilsRange.RANGE_7_9]" clickable
                @click="form.noUtensils = NoUtensilsRange.RANGE_7_9">
                <template #right-icon><van-radio :name="NoUtensilsRange.RANGE_7_9" /></template>
              </van-cell>
            </van-radio-group>
          </template>
        </van-field>
        <van-field name="noUtensilsPhotos" :rules="[
          { required: form.noUtensils !== '', message: '请上传不要餐具照片' }
        ]">
          <template #input>
            <van-uploader v-model="form.noUtensilsFileList" :max-count="10" :before-read="beforeRead"
              :after-read="afterRead" accept="image/*" @oversize="onOversize" :preview-image="true" />
          </template>
        </van-field>
      </van-cell-group>

      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">提交问卷</van-button>
      </div>
    </van-form>
    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeMount } from "vue";
import { useRouter } from "vue-router";
import { showToast, showDialog } from "vant";
import { showSuccessToast, showFailToast } from "../utils/toast";
import { closeLoading } from "@/components/Loading";
import dayjs from "dayjs";
import {
  StepCountRange,
  StepCountRangeText,
  StepCountPoints,
  BikeUsageRange,
  BikeUsageRangeText,
  BikeUsagePoints,
  PublicTransportRange,
  PublicTransportRangeText,
  PublicTransportPoints,
  NoUtensilsRange,
  NoUtensilsRangeText,
  NoUtensilsPoints
} from "@/enums/index";
import { useUserStore } from "../pinia/modules/user";
import { submitSurvey } from "../api/organization";
import TabBar from "@/components/TabBar.vue";

const router = useRouter();
const preparationStatus = ref("");
const showFullSurvey = ref(false);
const userStore = useUserStore();

// 在组件挂载前检查登录状态
onBeforeMount(() => {
  // 验证用户登录状态
  if (!userStore.checkLoginStatus()) {
    router.push("/");
    return;
  }
});

// 检查是否是从登录页面进入
const checkFromLogin = () => {
  const fromRoute = router.currentRoute.value.query.from;
  if (fromRoute === "login") {
    showDialog({
      title: "温馨提示",
      message: "每日答题都是一次性提交，不得进行修改，请大家谨慎提交",
      confirmButtonText: "我知道了"
    });
  }
};

// 监听路由变化
onMounted(() => {
  // 检查是否是从登录页面进入
  checkFromLogin();
});

const form = ref<any>({
  name: userStore.userInfo.userName, // 模拟数据，实际应从用户信息获取
  department: userStore.userInfo.deptName, // 模拟数据，实际应从用户信息获取
  stepCount: "",
  bikeUsage: "",
  publicTransport: "",
  noUtensils: "",
  stepCountFileList: [],
  bikeUsageFileList: [],
  publicTransportFileList: [],
  noUtensilsFileList: []
});

// 处理准备状态变化
const handlePreparationChange = (value: any) => {
  if (value === "notReady") {
    showToast("请准备好相关数据后再继续");
  }
};

// 返回准备
const goBack = () => {
  userStore.logout();
  router.push("/");
  // 这里可以添加返回上一页或其他逻辑
};

// 继续填写问卷
const proceedToSurvey = async () => {
  showFullSurvey.value = true;
  // const res = await getCheckInDetail({
  //   userCode: userStore.userInfo.userCode,
  //   checkDate: dayjs().format("YYYYMMDD")
  // });
  // console.log(res);
  // if (res.code === 0 && res.data) {
  //   todayEdit.value = true;
  //   form.value.stepCount = res.data.stepType;
  //   form.value.bikeUsage = res.data.bicycleType;
  //   form.value.publicTransport = res.data.busType;
  //   form.value.noUtensils = res.data.takeoutNoTablewareType;
  //   form.value.stepCountFileList = res.data.stepFileList || [];
  //   form.value.bikeUsageFileList = res.data.bicycleFileList || [];
  //   form.value.publicTransportFileList = res.data.busFileList || [];
  //   form.value.noUtensilsFileList = res.data.takeoutNoTablewareFileList || [];
  // } else {
  //   todayEdit.value = false;
  //   form.value = {
  //     name: userStore.userInfo.userName, // 模拟数据，实际应从用户信息获取
  //     department: userStore.userInfo.deptName, // 模拟数据，实际应从用户信息获取
  //     stepCount: "",
  //     bikeUsage: "",
  //     publicTransport: "",
  //     noUtensils: "",
  //     stepCountFileList: [],
  //     bikeUsageFileList: [],
  //     publicTransportFileList: [],
  //     noUtensilsFileList: []
  //   };
  // }
  // showToast("请开始填写问卷");
};

const onOversize = () => {
  showToast("文件大小不能超过 5MB");
};

// 上传前校验
const beforeRead = (file: File) => {
  // 校验文件类型
  if (!/\.(jpg|jpeg|png|gif|webp)$/i.test(file.name)) {
    showToast("请上传图片格式文件");
    return false;
  }

  // 校验文件大小，限制为5
  if (file.size > 5 * 1024 * 1024) {
    showToast("文件大小不能超过5MB");
    return false;
  }

  return true;
};

// 上传后处理
const afterRead = async (fileItem: any) => {
  const file = fileItem.file;
  const formData = new FormData();
  formData.append("file", file);
  try {
    const res = await fetch(`${window.location.origin}/cph/app/upload`, {
      method: "POST",
      body: formData
    });
    const result = await res.json();
    if (res.ok) {
      showToast("上传成功");
      fileItem.url = result.data.url;
      fileItem.fileMD5 = result.data.fileMD5;
    } else {
      showToast(result.message || "上传失败");
    }
  } catch (error) {
    console.error(error);
    showToast("网络错误，上传失败");
  }
};
// 清除选择
const clearCount = (key: string) => {
  form.value[key] = "";
  form.value[`${key}FileList`] = [];
};

const onSubmit = async () => {
  // 先验证所有已选项是否都上传了照片
  const selectedFields = [
    { field: "stepCount", name: "微信步数" },
    { field: "bikeUsage", name: "共享单车" },
    { field: "publicTransport", name: "班车/地铁/公交" },
    { field: "noUtensils", name: "外卖不要餐具" }
  ];

  // 检查每个已选项是否有上传照片
  for (const { field, name } of selectedFields) {
    const fileListField = `${field}FileList`;
    if (form.value[field] && form.value[fileListField].length === 0) {
      showToast(`您选择了${name}项，请上传对应照片`);
      return;
    }
  }

  // 如果所有选择的项目都有照片，或者什么都没选，则提交表单
  // 显示加载中状态
  // openLoading("提交中...");
  console.log(form.value);
  let userInfo = {
    userName: userStore.userInfo.userName,
    userCode: userStore.userInfo.userCode,
    deptCode: userStore.userInfo.deptCode[0],
    deptName: userStore.userInfo.deptName,
    secCode: userStore.userInfo.secCode[0],
    secName: userStore.userInfo.secName,
    checkDate: dayjs().format("YYYYMMDD"),
    stepType: form.value.stepCount,
    bicycleType: form.value.bikeUsage,
    busType: form.value.publicTransport,
    takeoutNoTablewareType: form.value.noUtensils,
    stepScore: StepCountPoints[form.value.stepCount] || 0,
    bicycleScore: BikeUsagePoints[form.value.bikeUsage] || 0,
    busScore: PublicTransportPoints[form.value.publicTransport] || 0,
    takeoutNoTablewareScore: NoUtensilsPoints[form.value.noUtensils] || 0,
    stepFile: form.value.stepCountFileList
      .map((item: any) => item.fileMD5)
      .join(","),
    bicycleFile: form.value.bikeUsageFileList
      .map((item: any) => item.fileMD5)
      .join(","),
    busFile: form.value.publicTransportFileList
      .map((item: any) => item.fileMD5)
      .join(","),
    takeoutNoTablewareFile: form.value.noUtensilsFileList
      .map((item: any) => item.fileMD5)
      .join(",")
  };
  // 检查是否什么都没选
  const hasSelection = selectedFields.some(({ field }) => form.value[field]);
  // 显示成功提示
  if (hasSelection) {
    const res = await submitSurvey(userInfo);
    console.log(res);
    if (res.code === 0) {
      showSuccessToast("问卷提交成功");
      // clearToast();
      closeLoading();
      // 模拟提交后的数据清空
      for (const { field } of selectedFields) {
        form.value[field] = "";
        form.value[`${field}FileList`] = [];
      }

      // 重置问卷状态
      showFullSurvey.value = false;
      preparationStatus.value = "";
      router.push("/profile");
    } else {
      showFailToast(res.message);
    }
  } else {
    showSuccessToast("空白问卷提交成功");
    // clearToast();
    closeLoading();
    // 重置问卷状态
    showFullSurvey.value = false;
    preparationStatus.value = "";
  }
};

// 修改上传验证函数
const validatePhotos = (field: string) => {
  const fileListField = `${field}FileList`;
  if (form.value[field] && form.value[fileListField]?.length === 0) {
    const messages: Record<string, string> = {
      stepCount: "请上传步数照片",
      bikeUsage: "请上传骑行照片",
      publicTransport: "请上传乘车照片",
      noUtensils: "请上传不要餐具照片"
    };
    showToast(messages[field]);
  }
};

onMounted(() => {
  // 清除标记，避免重复显示
  sessionStorage.removeItem("fromLogin");
  // 这里可以添加获取用户信息的逻辑
});
</script>

<style scoped>
.survey-container {
  padding-top: 46px;
  padding-bottom: 70px;
  background-color: #f7f8fa;
  height: 100vh;
  width: 100vw;
}

.survey-header {
  text-align: center;
}

.survey-title {
  font-weight: bold;
  color: #000;
  margin-bottom: 10px;
}

.download-link {
  color: #1989fa;
  font-size: 14px;
  text-decoration: underline;
}

.preparation-section {
  margin-bottom: 20px;
}

.radio-content {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}

.radio-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  line-height: 1.5;
}

.preview-cover {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
}

.preview-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 自定义标题样式 */
:deep(.van-cell-group__title) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 10px;
  font-weight: bold;
  color: #000;
  font-size: 16px;
}

.clear-selection {
  text-align: right;
  font-size: 12px;
  color: #999;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #000;
}
</style>
