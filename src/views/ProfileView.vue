# 更新为打卡记录页面
<template>
  <div class="profile-container">
    <van-nav-bar title="打卡记录" />
    <!-- 使用van-list实现上拉加载 -->
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
      error-text="请求失败，点击重新加载"
      :error="error"
      @error="onLoad"
    >
      <div
        v-for="(record, index) in records"
        :key="index"
        class="card-container"
      >
        <div class="info-card">
          <div class="info-row">
            <div class="info-item">
              <span class="label">姓名</span>
              <span class="value">{{ record.userName }}</span>
            </div>
            <div class="info-item">
              <span class="label">部门</span>
              <span class="value">{{ record.deptName }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">科室</span>
              <span class="value">{{ record.secName }}</span>
            </div>
            <div class="info-item">
              <span class="label">工号</span>
              <span class="value">{{ record.userCode }}</span>
            </div>
          </div>
          <div class="divider"></div>
          <div class="score-row">
            <div class="score-title">本日打卡总分</div>
            <div class="score-value">{{ record.totalScore }}</div>
          </div>
        </div>
      </div>

      <!-- 退出登录 -->
      <!-- <div class="logout-btn" @click="logout">退出登录</div> -->
    </van-list>
    <!-- 底部导航栏 -->
    <TabBar />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onBeforeMount } from "vue";
import { useUserStore } from "@/pinia/modules/user";
import { getScoreRecord } from "@/api/organization";
import { showFailToast } from "@/utils/toast";
import TabBar from "@/components/TabBar.vue";
import { useRouter } from "vue-router";

const router = useRouter();
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

// 在组件挂载前检查登录状态
onBeforeMount(() => {
  // 验证用户登录状态
  if (!userStore.checkLoginStatus()) {
    router.push("/");
    return;
  }
});

// 打卡记录
const records = ref([]);
const page = ref(1);
const pageSize = ref(10);
const loading = ref(false);
const finished = ref(false);
const error = ref(false);

const onLoad = async () => {
  try {
    loading.value = true;
    error.value = false;

    const res = await getScoreRecord({
      userCode: userInfo.value.userCode,
      page: page.value,
      pageSize: pageSize.value
    });

    if (res.code === 0) {
      const newRecords = res.data.list || [];
      records.value = [...records.value, ...newRecords];

      // 判断是否加载完成
      if (newRecords.length < pageSize.value || res.data.isLastPage) {
        finished.value = true;
      } else {
        page.value++;
      }
    } else {
      error.value = true;
      showFailToast(res.message || "加载失败");
    }
  } catch (e) {
    error.value = true;
    showFailToast("网络错误，请稍后再试");
  } finally {
    loading.value = false;
  }
};

// const logout = () => {
//   userStore.logout();
//   router.push("/");
// };

onMounted(() => {
  // 初次加载时不需要手动调用onLoad，van-list会自动调用
});
</script>

<style scoped>
.profile-container {
  height: 100vh;
  width: 100vw;
  background-color: #f7f8fa;
  padding-bottom: 70px;
  padding-top: 46px;
}

.card-container {
  padding: 16px;
}

.info-card {
  background-color: white;
  border-radius: 12px;
  padding: 10px 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-row:last-of-type {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.label {
  color: #969799;
  font-size: 14px;
  margin-bottom: 4px;
}

.value {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.divider {
  height: 1px;
  background-color: #f2f3f5;
}

.score-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.score-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.score-value {
  font-size: 24px;
  font-weight: bold;
  color: #1989fa;
}

/* 历史记录卡片样式 */
.record-card {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.record-date {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 12px;
}

.record-content {
  display: flex;
  justify-content: space-between;
}

.record-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.record-item {
  display: flex;
  flex-direction: column;
}

.record-score {
  font-size: 22px;
  font-weight: bold;
  color: #ff976a;
  align-self: center;
}
</style>
