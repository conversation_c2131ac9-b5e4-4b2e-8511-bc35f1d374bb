<template>
  <div class="register-container">
    <van-nav-bar title="注册账号" left-arrow @click-left="onClickLeft" />

    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <!-- 姓名 -->
        <van-field v-model="userName" name="name" label="姓名" placeholder="请输入姓名"
          :rules="[{ required: true, message: '请输入姓名' }]" />
        <!-- 部门选择 -->
        <van-field v-model="departmentName" is-link readonly name="department" label="部门" placeholder="请选择部门"
          @click="showDepartmentPicker = true" :rules="[{ required: true, message: '请选择部门' }]" />
        <van-popup v-model:show="showDepartmentPicker" position="bottom">
          <van-picker :columns="departmentList" @confirm="onDepartmentConfirm" @cancel="showDepartmentPicker = false"
            show-toolbar title="选择部门" />
        </van-popup>

        <!-- 科室选择 -->
        <van-field v-model="officeName" is-link readonly name="office" label="科室" placeholder="请选择科室"
          @click="showOfficePicker = true" :disabled="!selectedDepartment"
          :rules="[{ required: true, message: '请选择科室' }]" />
        <van-popup v-model:show="showOfficePicker" position="bottom">
          <van-picker :columns="officeList" @confirm="onOfficeConfirm" @cancel="showOfficePicker = false" show-toolbar
            title="选择科室" />
        </van-popup>

        <!-- 工号输入 -->
        <van-field v-model="workId" name="workId" label="工号" placeholder="请输入工号"
          :rules="[{ required: true, message: '请填写工号' }]" />
      </van-cell-group>

      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          注册
        </van-button>
        <div class="login-link">
          已有账号？<router-link to="/">返回登录</router-link>
        </div>
      </div>
    </van-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  showSuccessToast,
  showFailToast,
  showLoadingToast,
  clearToast
} from "../utils/toast";
import { useUserStore } from "../pinia/modules/user";
import {
  getDepartments,
  getOfficesByDepartment,
  registerUser
} from "@/api/organization";

const router = useRouter();
const userStore = useUserStore();

// 部门相关
const departmentName = ref("");
const selectedDepartment = ref(null);
const departmentList = ref([]);
const showDepartmentPicker = ref(false);

// 科室相关
const officeName = ref("");
const selectedOffice = ref(null);
const officeList = ref([]);
const showOfficePicker = ref(false);

// 其他字段
const workId = ref("");
const userName = ref("");

// 获取部门列表
const fetchDepartmentList = async () => {
  try {
    let res = await getDepartments();
    if (res.code === 0) {
      departmentList.value = res.data.map((item) => ({
        text: item.deptName,
        value: item.deptCode
      }));
    }
  } catch (error) {
    clearToast();
    showFailToast("获取部门列表失败");
  }
};

// 获取科室列表
const fetchOfficeList = async (departmentId) => {
  try {
    showLoadingToast("加载科室列表...");
    let res = await getOfficesByDepartment(departmentId);
    officeList.value = res.data.map((item) => ({
      text: item.secName,
      value: item.secCode
    }));
    clearToast();
  } catch (error) {
    showFailToast("获取科室列表失败");
    clearToast();
  }
};

// 部门选择确认
const onDepartmentConfirm = ({ selectedValues, selectedOptions }) => {
  selectedDepartment.value = selectedValues;
  departmentName.value = selectedOptions[0]?.text;
  showDepartmentPicker.value = false;

  // 重置科室
  officeName.value = "";
  selectedOffice.value = null;

  // 获取对应的科室列表
  fetchOfficeList(selectedValues);
};

// 科室选择确认
const onOfficeConfirm = ({ selectedValues, selectedOptions }) => {
  selectedOffice.value = selectedValues;
  officeName.value = selectedOptions[0]?.text;
  showOfficePicker.value = false;
};

const onSubmit = async () => {
  try {
    const res = await registerUser({
      deptCode: selectedDepartment.value[0],
      deptName: departmentName.value,
      secCode: selectedOffice.value[0],
      secName: officeName.value,
      userCode: workId.value,
      userName: userName.value
    });
    if (res.code === 0) {
      showSuccessToast("注册成功");
      router.push("/");
    } else {
      showFailToast(res.message);
    }
  } catch (error) {
    console.error("注册错误:", error);
  }
};

const onClickLeft = () => {
  router.push("/");
};

// 初始化获取部门列表
onMounted(async () => {
  await fetchDepartmentList();
});
</script>

<style scoped>
.register-container {
  padding-top: 46px;
  height: 100vh;
}

.login-link {
  margin-top: 16px;
  text-align: center;
  font-size: 14px;
}
</style>
