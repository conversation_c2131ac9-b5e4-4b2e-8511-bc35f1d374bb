import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";
import { useUserStore } from "../pinia/modules/user";
import { showDialog } from "vant";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "login",
    component: () => import("../views/LoginView.vue")
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("../views/RegisterView.vue")
  },
  {
    path: "/survey",
    name: "Survey",
    component: () => import("../views/SurveyView.vue"),
    meta: { requiresAuth: true }
  },
  {
    path: "/profile",
    name: "Profile",
    component: () => import("../views/ProfileView.vue"),
    meta: { requiresAuth: true }
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

// 添加一个变量来跟踪用户是否通过正常登录流程
let isAuthenticated = false;

// 导航守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore();

  // 如果用户登录了，设置认证标志
  if (from.path === "/" && to.path === "/survey" && userStore.isLoggedIn) {
    isAuthenticated = true;
    sessionStorage.setItem("isAuthenticated", "true");
  }

  // 如果是登录页，重置认证标志
  if (to.path === "/") {
    isAuthenticated = false;
    sessionStorage.removeItem("isAuthenticated");
  }

  // 从sessionStorage恢复认证状态（处理页面刷新的情况）
  if (sessionStorage.getItem("isAuthenticated") === "true") {
    isAuthenticated = true;
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 检查用户是否登录
    if (!userStore.isLoggedIn) {
      // 用户未登录，重定向到登录页
      next({ path: "/" });
      return;
    }

    // 检查是否是通过浏览器前进/后退按钮导航
    // 如果不是通过正常登录流程，且目标是需要认证的页面
    if (!isAuthenticated && to.path !== from.path) {
      // 检查登录状态是否有效
      if (!userStore.checkLoginStatus()) {
        // 登录状态无效，重定向到登录页
        userStore.logout(); // 确保清除任何过期的登录状态
        next({ path: "/" });
        return;
      }
    }
  }

  // 如果是从登录页到打卡页面，添加标记
  // 修复无限重定向问题：检查是否已经有from=login查询参数
  if (to.path === "/survey" && from.path === "/" && !to.query.from) {
    next({
      path: "/survey",
      query: { from: "login" }
    });
  } else {
    next();
  }
});

export default router;
