/**
 * 步数范围枚举
 * 用于表示用户步数统计的不同范围区间
 */
export enum StepCountRange {
  RANGE_20K_25K = "5000-10000",
  RANGE_25K_30K = "10001-15000",
  RANGE_30K_35K = "15001-20000",
  RANGE_ABOVE_35K = "20000+"
}

/**
 * 步数范围显示文本
 * 用于在界面上显示友好的步数范围文本
 */
export const StepCountRangeText = {
  [StepCountRange.RANGE_20K_25K]: "A. 5,000步~10,001步",
  [StepCountRange.RANGE_25K_30K]: "B. 10,001步~15,000步",
  [StepCountRange.RANGE_30K_35K]: "C. 15,001步~20,000步",
  [StepCountRange.RANGE_ABOVE_35K]: "D. 20,000步以上"
};

/**
 * 步数范围对应的积分
 */
export const StepCountPoints = {
  [StepCountRange.RANGE_20K_25K]: 4,
  [StepCountRange.RANGE_25K_30K]: 6,
  [StepCountRange.RANGE_30K_35K]: 9,
  [StepCountRange.RANGE_ABOVE_35K]: 12
};

/**
 * 共享单车使用次数范围枚举
 */
export enum BikeUsageRange {
  RANGE_3_5 = "1",
  RANGE_6_8 = "2",
  RANGE_9_12 = "2+",
}

/**
 * 共享单车使用次数范围显示文本
 */
export const BikeUsageRangeText = {
  [BikeUsageRange.RANGE_3_5]: "A. 1次",
  [BikeUsageRange.RANGE_6_8]: "B. 2次",
  [BikeUsageRange.RANGE_9_12]: "C. 2次以上",
};

/**
 * 共享单车使用次数对应的积分
 */
export const BikeUsagePoints = {
  [BikeUsageRange.RANGE_3_5]: 3,
  [BikeUsageRange.RANGE_6_8]: 4,
  [BikeUsageRange.RANGE_9_12]: 5,
};

/**
 * 班车/地铁/公交使用次数范围枚举
 */
export enum PublicTransportRange {
  RANGE_BELOW_4 = "1",
  RANGE_4_6 = "2",
  RANGE_7_9 = "2+",
}

/**
 * 班车/地铁/公交使用次数范围显示文本
 */
export const PublicTransportRangeText = {
  [PublicTransportRange.RANGE_BELOW_4]: "A. 1次",
  [PublicTransportRange.RANGE_4_6]: "B. 2次",
  [PublicTransportRange.RANGE_7_9]: "C. 2次以上",
};

/**
 * 班车/地铁/公交使用次数对应的积分
 */
export const PublicTransportPoints = {
  [PublicTransportRange.RANGE_BELOW_4]: 3,
  [PublicTransportRange.RANGE_4_6]: 4,
  [PublicTransportRange.RANGE_7_9]: 5,
};

/**
 * 外卖不要餐具次数范围枚举
 */
export enum NoUtensilsRange {
  RANGE_BELOW_4 = "1",
  RANGE_4_6 = "2",
  RANGE_7_9 = "2+",
}

/**
 * 外卖不要餐具次数范围显示文本
 */
export const NoUtensilsRangeText = {
  [NoUtensilsRange.RANGE_BELOW_4]: "A. 1次",
  [NoUtensilsRange.RANGE_4_6]: "B. 2次",
  [NoUtensilsRange.RANGE_7_9]: "C. 2次以上",
};

/**
 * 外卖不要餐具次数对应的积分
 */
export const NoUtensilsPoints = {
  [NoUtensilsRange.RANGE_BELOW_4]: 1,
  [NoUtensilsRange.RANGE_4_6]: 2,
  [NoUtensilsRange.RANGE_7_9]: 3,
};

/**
 * 获取步数范围的显示文本
 * @param range 步数范围枚举值
 * @returns 对应的显示文本
 */
export function getStepCountRangeText(range: StepCountRange): string {
  return StepCountRangeText[range] || "未知范围";
}

/**
 * 获取步数范围对应的积分
 * @param range 步数范围枚举值
 * @returns 对应的积分
 */
export function getStepCountPoints(range: StepCountRange): number {
  return StepCountPoints[range] || 0;
}

/**
 * 获取共享单车使用次数范围的显示文本
 * @param range 共享单车使用次数范围枚举值
 * @returns 对应的显示文本
 */
export function getBikeUsageRangeText(range: BikeUsageRange): string {
  return BikeUsageRangeText[range] || "未知范围";
}

/**
 * 获取共享单车使用次数对应的积分
 * @param range 共享单车使用次数范围枚举值
 * @returns 对应的积分
 */
export function getBikeUsagePoints(range: BikeUsageRange): number {
  return BikeUsagePoints[range] || 0;
}

/**
 * 获取班车/地铁/公交使用次数范围的显示文本
 * @param range 班车/地铁/公交使用次数范围枚举值
 * @returns 对应的显示文本
 */
export function getPublicTransportRangeText(
  range: PublicTransportRange
): string {
  return PublicTransportRangeText[range] || "未知范围";
}

/**
 * 获取班车/地铁/公交使用次数对应的积分
 * @param range 班车/地铁/公交使用次数范围枚举值
 * @returns 对应的积分
 */
export function getPublicTransportPoints(range: PublicTransportRange): number {
  return PublicTransportPoints[range] || 0;
}

/**
 * 获取外卖不要餐具次数范围的显示文本
 * @param range 外卖不要餐具次数范围枚举值
 * @returns 对应的显示文本
 */
export function getNoUtensilsRangeText(range: NoUtensilsRange): string {
  return NoUtensilsRangeText[range] || "未知范围";
}

/**
 * 获取外卖不要餐具次数对应的积分
 * @param range 外卖不要餐具次数范围枚举值
 * @returns 对应的积分
 */
export function getNoUtensilsPoints(range: NoUtensilsRange): number {
  return NoUtensilsPoints[range] || 0;
}

/**
 * 计算总积分
 * @param stepCount 步数范围
 * @param bikeUsage 共享单车使用次数范围
 * @param publicTransport 班车/地铁/公交使用次数范围
 * @param noUtensils 外卖不要餐具次数范围
 * @returns 总积分
 */
export function calculateTotalPoints(
  stepCount: StepCountRange,
  bikeUsage: BikeUsageRange,
  publicTransport: PublicTransportRange,
  noUtensils: NoUtensilsRange
): number {
  return (
    getStepCountPoints(stepCount) +
    getBikeUsagePoints(bikeUsage) +
    getPublicTransportPoints(publicTransport) +
    getNoUtensilsPoints(noUtensils)
  );
}
