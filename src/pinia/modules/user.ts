import { defineStore } from "pinia";

// 从localStorage获取初始状态
const getStoredState = () => {
  const storedState = localStorage.getItem("userState");
  if (storedState) {
    try {
      return JSON.parse(storedState);
    } catch (e) {
      console.error("Error parsing stored state:", e);
    }
  }
  return {
    isLoggedIn: false,
    loginTimestamp: 0,
    userInfo: {
      userName: "",
      userCode: "",
      deptCode: "",
      deptName: "",
      secCode: "",
      secName: "",
      remark: "",
      gender: "",
      phone: ""
    }
  };
};

// 保存状态到localStorage
const saveState = (state: any) => {
  localStorage.setItem(
    "userState",
    JSON.stringify({
      isLoggedIn: state.isLoggedIn,
      loginTimestamp: state.loginTimestamp,
      userInfo: state.userInfo
    })
  );
};

// 会话超时时间（毫秒）- 设置为30分钟
const SESSION_TIMEOUT = 30 * 60 * 1000;

export const useUserStore = defineStore("user", {
  state: () => getStoredState(),

  getters: {
    getIsLoggedIn: (state) => state.isLoggedIn
  },

  actions: {
    loginWithDetails(loginData: any, userInfo: any) {
      this.isLoggedIn = true;
      this.loginTimestamp = Date.now();
      this.userInfo = {
        ...this.userInfo,
        ...loginData
      };

      // 保存到localStorage
      saveState(this);
      console.log("登录成功，用户信息:", this.userInfo);

      // 设置会话存储，用于跟踪前进/后退导航
      sessionStorage.setItem("isAuthenticated", "true");

      return true;
    },

    register(userData: any) {
      console.log("注册用户数据:", userData);
      return true;
    },

    logout() {
      this.isLoggedIn = false;
      this.loginTimestamp = 0;
      this.userInfo = {
        userName: "",
        userCode: "",
        deptCode: "",
        deptName: "",
        secCode: "",
        secName: "",
        remark: "",
        gender: "",
        phone: ""
      };

      // 保存到localStorage
      saveState(this);

      // 清除会话存储
      sessionStorage.removeItem("isAuthenticated");
    },

    checkLoginStatus() {
      // 检查是否登录
      if (!this.isLoggedIn) {
        return false;
      }

      // 检查会话是否过期
      const currentTime = Date.now();
      if (currentTime - this.loginTimestamp > SESSION_TIMEOUT) {
        // 会话已过期，自动登出
        this.logout();
        return false;
      }

      // 更新登录时间戳
      this.loginTimestamp = currentTime;
      saveState(this);

      return true;
    }
  }
});

export default useUserStore;
