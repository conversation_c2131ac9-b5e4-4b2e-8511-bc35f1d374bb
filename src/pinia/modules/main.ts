import { defineStore } from "pinia";

// main is the name of the store. It is unique across your application
// and will appear in devtools
export const useMainStore = defineStore("main", {
  // a function that returns a fresh state
  state: () => ({
    counter: 0,
    name: "<PERSON>",
    selectAddress: null,
    undoneTodoList: [
      {
        id: Date.now(),
        name: "吃饭"
      }
    ]
  }),
  // optional getters
  getters: {
    // getters receive the state as first parameter
    doubleCount: (state) => state.counter * 2,
    // use getters in other getters
    doubleCountPlusOne(): number {
      return this.doubleCount * 2 + 1;
    }
  },
  // optional actions
  actions: {
    reset() {
      // `this` is the store instance
      this.counter = 0;
    }
  },
  // 启用持久化
  persist: {
    key: "main-store",
    storage: localStorage
  }
});

export default useMainStore;
