input {
  border: 1px solid #dedede;
  border-radius: 5px;
  outline: none;
  text-indent: 2px;
  height: 30px;
  line-height: 26px;
}

.mg5 {
  margin: 5px;
}

.mg10 {
  margin: 10px;
}

.mg15 {
  margin: 15px;
}

.mg20 {
  margin: 20px;
}

.mg-t0 {
  margin-top: 0;
}

.mg-r0 {
  margin-right: 0;
}

.mg-b0 {
  margin-bottom: 0;
}

.mg-l0 {
  margin-left: 0;
}

.mg-t5 {
  margin-top: 5px;
}

.mg-r5 {
  margin-right: 5px;
}

.mg-b5 {
  margin-bottom: 5px;
}

.mg-l5 {
  margin-left: 5px;
}

.mg-t10 {
  margin-top: 10px;
}

.mg-r10 {
  margin-right: 10px;
}

.mg-b10 {
  margin-bottom: 10px;
}

.mg-l10 {
  margin-left: 10px;
}

.mg-t15 {
  margin-top: 15px;
}

.mg-r15 {
  margin-right: 15px;
}

.mg-b15 {
  margin-bottom: 15px;
}

.mg-l15 {
  margin-left: 15px;
}

.mg-t20 {
  margin-top: 20px;
}

.mg-r20 {
  margin-right: 20px;
}

.mg-b20 {
  margin-bottom: 20px;
}

.mg-l20 {
  margin-left: 20px;
}

.pd5 {
  padding: 5px;
}

.pd10 {
  padding: 10px;
}

.pd15 {
  padding: 15px;
}

.pd20 {
  padding: 20px;
}

.pd-t0 {
  padding-top: 0;
}

.pd-r0 {
  padding-right: 0;
}

.pd-b0 {
  padding-bottom: 0;
}

.pd-l0 {
  padding-left: 0;
}

.pd-t5 {
  padding-top: 5px;
}

.pd-r5 {
  padding-right: 5px;
}

.pd-b5 {
  padding-bottom: 5px;
}

.pd-l5 {
  padding-left: 5px;
}

.pd-t10 {
  padding-top: 10px;
}

.pd-r10 {
  padding-right: 10px;
}

.pd-b10 {
  padding-bottom: 10px;
}

.pd-l10 {
  padding-left: 10px;
}

.pd-t15 {
  padding-top: 15px;
}

.pd-r15 {
  padding-right: 15px;
}

.pd-b15 {
  padding-bottom: 15px;
}

.pd-l15 {
  padding-left: 15px;
}

.pd-t20 {
  padding-top: 20px;
}

.pd-r20 {
  padding-right: 20px;
}

.pd-b20 {
  padding-bottom: 20px;
}

.pd-l20 {
  padding-left: 20px;
}

.fs12 {
  font-size: 12px;
}

.cred {
  color: red;
}

.pd-nav {
  padding-top: 46px;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}