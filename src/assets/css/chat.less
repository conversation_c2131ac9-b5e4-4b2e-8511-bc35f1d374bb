@blue-color: #16b5eb;
@green-color: #41b883;

.chat-wrapper {
  .avatar {
    flex: none;
    margin-right: 5px;
    background: url('https://img.yzcdn.cn/vant/logo.png') no-repeat 0 0 / 100% 100%;
    width: 50px;
    height: 50px;
    border-radius: 30px;
  }

  .bubble {
    padding: 10px 15px;
    color: #333;
    font-size: 14px;
    line-height: 22px;
    background: #fff;
    border: 1px solid @green-color;
    border-radius: 5px;
    position: relative;
    min-width: 80px;
    margin: 0 15px 0 5px;

    img {
      width: 100%;
    }
  }

  .bubble:before {
    content: '';
    border-style: solid;
    border-width: 8px;
    position: absolute;
    top: 8px;
  }

  .bubble-left:before {
    border-color: transparent @green-color transparent transparent;
    left: -17px;
  }

  .bubble-right:before {
    border-color: transparent transparent transparent @blue-color;
    right: -17px;
  }

  .bubble-right {
    color: #fff;
    background: @blue-color;
    border: 1px solid transparent;
  }
}

.answers-wrapper {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 10px;
  background: #fff;

  .btnbox {
    flex: 0 0 50%;

    .btn {
      background: @blue-color;
      margin: 5px;
      padding: 8px 10px;
      border-radius: 5px;
      color: #fff;
      cursor: pointer;
      font-size: 12px;
      line-height: 18px;
      transition: 0.2s;
      width: 100%;

      &:active {
        background: #1296c3;
      }
    }
  }

  .loadingtxt {
    color: @blue-color;
  }
}
