# 创建底部导航栏组件
<template>
  <div class="tab-bar-container">
    <van-tabbar v-model="active" route fixed placeholder>
      <van-tabbar-item replace to="/survey" icon="todo-list-o"
        >打卡</van-tabbar-item
      >
      <van-tabbar-item replace to="/profile" icon="records"
        >打卡记录</van-tabbar-item
      >
    </van-tabbar>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const active = ref(0);

// 根据当前路由初始化活动标签
if (route.path === "/profile") {
  active.value = 1;
} else {
  active.value = 0;
}
</script>

<style scoped>
.tab-bar-container {
  width: 100%;
}
</style>
