{"name": "vue-demo", "version": "1.2.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:type": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@types/axios": "^0.14.4", "@vant/area-data": "^1.4.1", "axios": "^1.9.0", "dayjs": "^1.11.7", "pinia": "^2.0.36", "pinia-plugin-persistedstate": "^4.3.0", "vant": "^4.3.1", "vue": "^3.2.47", "vue-router": "^4.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.1.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "autoprefixer": "^10.4.14", "less": "^4.1.3", "less-loader": "^11.1.0", "postcss-pxtorem": "^6.0.0", "terser": "^5.17.5", "typescript": "^5.0.2", "unplugin-vue-components": "^0.24.1", "vite": "^4.3.2", "vue-tsc": "^1.4.2"}}