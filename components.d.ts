/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    Button: typeof import('./src/components/base/Button.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Select: typeof import('./src/components/base/Select.vue')['default']
    TabBar: typeof import('./src/components/TabBar.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanList: typeof import('vant/es')['List']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanUploader: typeof import('vant/es')['Uploader']
  }
}
