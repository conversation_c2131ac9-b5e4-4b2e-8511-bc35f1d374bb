import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "unplugin-vue-components/resolvers";
import vueJsx from "@vitejs/plugin-vue-jsx";

// https://vitejs.dev/config/
export default defineConfig({
  base: "./",
  server: {
    proxy: {
      "/cph": {
        target: "http://**************:9092/",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, "")
      }
    },
    host: true
  },
  build: {
    outDir: "dist",
    // 添加以下配置以忽略类型错误
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    // 关闭源码映射以减少构建大小
    sourcemap: false,
    // 设置为空数组以跳过类型检查
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["vue", "vue-router", "pinia"],
          vant: ["vant"]
        }
      }
    }
  },
  plugins: [
    vue(),
    vueJsx(),
    Components({
      resolvers: [VantResolver()]
    })
  ],
  resolve: {
    alias: {
      "@": "/src"
    }
  },
  // 添加以下配置以忽略类型错误
  optimizeDeps: {
    exclude: []
  }
});
